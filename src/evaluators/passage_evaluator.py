"""
读文章评测模块
实现普通话读文章测试的核心评测功能
"""

import whisper
import librosa
import numpy as np
from typing import Dict, List, Tuple, Any
import jieba
from pypinyin import lazy_pinyin, Style
from difflib import SequenceMatcher
import json
from datetime import datetime
import logging
import warnings
import os
import zhconv

from ..core.audio_processor import AudioProcessor, PitchExtractor
from ..core.phoneme_analyzer import PhonemeAnalyzer

# 屏蔽各种警告
warnings.filterwarnings("ignore", message="FP16 is not supported on CPU; using FP32 instead")
warnings.filterwarnings("ignore", message="n_fft=.* is too large for input signal of length=.*")
warnings.filterwarnings("ignore", message="With fmin=.*, sr=.* and frame_length=.*, less than two periods.*")
warnings.filterwarnings("ignore", message="fmin=.* is too small for frame_length=.* and sr=.*")
warnings.filterwarnings("ignore", message="Empty filters detected in mel frequency basis.*")

logger = logging.getLogger(__name__)


class PassageEvaluator:
    """读文章评测器"""
    
    def __init__(self):
        self.audio_processor = AudioProcessor()
        self.pitch_extractor = PitchExtractor()
        self.phoneme_analyzer = PhonemeAnalyzer()
        self.whisper_model = None
        self._load_whisper_model()

        # 评分权重配置 - 更新为四个维度
        self.weights = {
            "initial": 0.25,      # 声母权重25%
            "final": 0.25,        # 韵母权重25%
            "tone": 0.25,         # 声调权重25%
            "fluency": 0.25       # 流畅性权重25%
        }
        
        # 评分等级标准
        self.grade_standards = {
            "一级甲等": {"min": 97, "max": 100},
            "一级乙等": {"min": 92, "max": 96},
            "二级甲等": {"min": 87, "max": 91},
            "二级乙等": {"min": 80, "max": 86},
            "三级甲等": {"min": 70, "max": 79},
            "三级乙等": {"min": 60, "max": 69}
        }
    
    def _load_whisper_model(self):
        """加载Whisper模型"""
        try:
            # 使用base模型，中文识别效果更好
            self.whisper_model = whisper.load_model("base")
            logger.info("Whisper模型加载成功")
        except Exception as e:
            logger.error(f"Whisper模型加载失败: {e}")
            raise
    
    def evaluate_passage(self, audio_file: str, reference_text: str) -> Dict[str, Any]:
        """
        评测读文章
        
        Args:
            audio_file: 音频文件路径
            reference_text: 标准文本内容
            
        Returns:
            评测结果字典
        """
        try:
            # 1. 音频预处理
            audio, sr = self.audio_processor.load_audio(audio_file)
            processed_audio = self.audio_processor.preprocess_audio(audio)
            
            # 2. 语音识别
            recognized_text = self._speech_recognition(audio_file)

            # 3. 音素分析 (声母、韵母、声调)
            phoneme_result = self.phoneme_analyzer.analyze_phonemes(processed_audio, reference_text)

            # 4. 流畅性分析
            fluency_result = self._analyze_fluency(processed_audio, reference_text)

            # 5. 综合评分
            overall_score = self._calculate_overall_score_new(
                phoneme_result, fluency_result
            )

            # 6. 生成详细报告
            evaluation_result = self._generate_report_new(
                overall_score, phoneme_result, fluency_result,
                recognized_text, reference_text
            )
            
            return evaluation_result
            
        except Exception as e:
            logger.error(f"评测过程出错: {e}")
            return self._generate_error_report(str(e))
    
    def _speech_recognition(self, audio_file: str) -> str:
        """语音识别"""
        try:
            # 检查文件是否存在
            if not os.path.exists(audio_file):
                raise FileNotFoundError(f"音频文件不存在: {audio_file}")

            # 检查文件大小
            if os.path.getsize(audio_file) == 0:
                raise ValueError("音频文件为空")

            # 使用绝对路径，避免路径问题
            abs_audio_file = os.path.abspath(audio_file)

            result = self.whisper_model.transcribe(
                abs_audio_file,
                language="zh",
                verbose=False  # 减少输出信息
            )

            # 繁体转简体
            recognized_text = result["text"].strip()
            recognized_text = zhconv.convert(recognized_text, 'zh-cn')

            return recognized_text
        except Exception as e:
            logger.error(f"语音识别失败: {e}")
            return ""
    
    def _analyze_accuracy(self, recognized_text: str, reference_text: str) -> Dict[str, Any]:
        """分析准确性"""
        # 基于参考文本的同音字纠错
        corrected_text = self._correct_homophones_with_reference(recognized_text, reference_text)

        # 文本清理
        recognized_clean = self._clean_text(corrected_text)
        reference_clean = self._clean_text(reference_text)

        # 计算相似度
        similarity = SequenceMatcher(None, recognized_clean, reference_clean).ratio()
        accuracy_score = similarity * 100

        # 简单错误分析
        error_details = self._find_simple_errors(recognized_clean, reference_clean)

        return {
            "score": accuracy_score,
            "similarity": similarity * 100,
            "error_details": error_details,
            "recognized_text": corrected_text  # 返回纠错后的文本
        }
    
    def _analyze_fluency(self, audio: np.ndarray, reference_text: str) -> Dict[str, Any]:
        """分析流畅性"""
        # 简单的流畅性分析
        total_duration = len(audio) / self.audio_processor.sample_rate
        char_count = len(self._clean_text(reference_text))
        speech_rate = char_count / total_duration if total_duration > 0 else 0

        # 基于语速的简单评分
        if 3 <= speech_rate <= 6:
            fluency_score = 90
            rate_category = "适中"
        elif speech_rate < 3:
            fluency_score = max(70, 90 - (3 - speech_rate) * 10)
            rate_category = "偏慢"
        else:
            fluency_score = max(70, 90 - (speech_rate - 6) * 8)
            rate_category = "偏快"

        return {
            "score": fluency_score,
            "speech_rate": speech_rate,
            "speech_rate_category": rate_category,
            "total_duration": total_duration
        }
    
    def _analyze_naturalness(self, audio: np.ndarray, reference_text: str) -> Dict[str, Any]:
        """分析自然度"""
        # 简化的自然度分析
        try:
            f0 = self.pitch_extractor.extract_pitch(audio)
            f0_valid = f0[~np.isnan(f0)]

            if len(f0_valid) > 10:
                f0_mean = np.mean(f0_valid)
                f0_std = np.std(f0_valid)

                # 简单的自然度评分
                if 100 <= f0_mean <= 300 and 10 <= f0_std <= 50:
                    naturalness_score = 85
                    pitch_category = "正常"
                else:
                    naturalness_score = 75
                    pitch_category = "偏高" if f0_mean > 300 else "偏低"
            else:
                f0_mean = f0_std = 0
                naturalness_score = 70
                pitch_category = "检测困难"
        except:
            f0_mean = f0_std = 0
            naturalness_score = 75
            pitch_category = "无法检测"

        return {
            "score": naturalness_score,
            "pitch_mean": f0_mean,
            "pitch_category": pitch_category
        }
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        import re

        # 繁体转简体
        text = zhconv.convert(text, 'zh-cn')

        # 统一标点符号
        text = text.replace(',', '，').replace('.', '。').replace('!', '！').replace('?', '？')
        text = text.replace(';', '；').replace(':', '：').replace('"', '"').replace('"', '"')

        # 移除标点符号和空格，只保留中文字符
        cleaned = re.sub(r'[^\u4e00-\u9fff]', '', text)
        return cleaned

    def _correct_homophones_with_reference(self, recognized_text: str, reference_text: str) -> str:
        """基于参考文本的同音字纠错"""
        # 获取参考文本中的词汇
        reference_words = set(jieba.cut(reference_text))

        # 常见同音字映射
        homophone_map = {
            '赵': ['照'],
            '辣': ['腊', '蜡'],
            '动': ['冻', '东'],
            '结': ['节'],
            '距': ['矩'],
            '春': ['春'],  # 保持不变
            '家': ['家'],  # 保持不变
        }

        corrected_text = recognized_text

        # 对识别文本进行分词
        recognized_words = list(jieba.cut(recognized_text))

        for i, word in enumerate(recognized_words):
            # 检查每个词是否需要纠错
            if word not in reference_words and len(word) >= 1:
                # 尝试同音字替换
                for char_idx, char in enumerate(word):
                    if char in homophone_map:
                        for candidate in homophone_map[char]:
                            # 构造候选词
                            candidate_word = word[:char_idx] + candidate + word[char_idx+1:]

                            # 检查候选词是否在参考文本中
                            if candidate_word in reference_words:
                                corrected_text = corrected_text.replace(word, candidate_word, 1)
                                break

        return corrected_text

    # 保留旧版本函数以防兼容性问题
    def _calculate_overall_score(self, accuracy_result: Dict,
                               fluency_result: Dict, naturalness_result: Dict) -> float:
        """计算综合得分 - 旧版本兼容"""
        return self._calculate_overall_score_new(
            {'summary': {'initial_accuracy': 75, 'final_accuracy': 75, 'tone_accuracy': 75}},
            fluency_result
        )

    def _generate_report_new(self, overall_score: float, phoneme_result: Dict,
                            fluency_result: Dict, recognized_text: str, reference_text: str) -> Dict[str, Any]:
        """生成评测报告 - 新版本"""
        grade_level = self._get_grade_level(overall_score)
        summary = phoneme_result['summary']

        return {
            "timestamp": datetime.now().isoformat(),
            "overall_score": overall_score,
            "grade_level": grade_level,
            "detailed_analysis": {
                "initial_accuracy": round(summary["initial_accuracy"], 1),
                "final_accuracy": round(summary["final_accuracy"], 1),
                "tone_accuracy": round(summary["tone_accuracy"], 1),
                "fluency_score": round(fluency_result["score"], 1)
            },
            "phoneme_details": {
                "total_phonemes": summary["total_phonemes"],
                "initial_errors": summary["initial_errors"],
                "final_errors": summary["final_errors"],
                "tone_errors": summary["tone_errors"]
            },
            "timing_analysis": {
                "total_duration": round(fluency_result["total_duration"], 1),
                "speech_rate": round(fluency_result["speech_rate"], 1),
                "speech_rate_category": fluency_result["speech_rate_category"]
            },
            "text_comparison": {
                "reference_text": reference_text,
                "recognized_text": recognized_text
            }
        }
    
    def _find_simple_errors(self, recognized: str, reference: str) -> List[Dict[str, str]]:
        """简单错误分析"""
        errors = []

        # 长度差异分析
        len_diff = len(reference) - len(recognized)
        if len_diff > 0:
            errors.append({
                "type": "遗漏字符",
                "description": f"少读了约{len_diff}个字符"
            })
        elif len_diff < 0:
            errors.append({
                "type": "多读字符",
                "description": f"多读了约{-len_diff}个字符"
            })

        # 简单的相似度分析
        similarity = SequenceMatcher(None, reference, recognized).ratio()
        if similarity < 0.8:
            errors.append({
                "type": "发音差异较大",
                "description": f"整体相似度仅{similarity*100:.1f}%"
            })

        return errors[:5]
    

    
    def _calculate_overall_score_new(self, phoneme_result: Dict, fluency_result: Dict) -> float:
        """计算综合得分 - 新版本"""
        summary = phoneme_result['summary']

        overall_score = (
            summary["initial_accuracy"] * self.weights["initial"] +
            summary["final_accuracy"] * self.weights["final"] +
            summary["tone_accuracy"] * self.weights["tone"] +
            fluency_result["score"] * self.weights["fluency"]
        )
        return round(overall_score, 1)
    
    def _get_grade_level(self, score: float) -> str:
        """获取等级"""
        for grade, standard in self.grade_standards.items():
            if standard["min"] <= score <= standard["max"]:
                return grade
        return "三级乙等"
    
    def _generate_report(self, overall_score: float, accuracy_result: Dict,
                        fluency_result: Dict, naturalness_result: Dict,
                        recognized_text: str, reference_text: str) -> Dict[str, Any]:
        """生成评测报告"""
        grade_level = self._get_grade_level(overall_score)

        return {
            "timestamp": datetime.now().isoformat(),
            "overall_score": overall_score,
            "grade_level": grade_level,
            "detailed_analysis": {
                "pronunciation_accuracy": round(accuracy_result["score"], 1),
                "fluency_score": round(fluency_result["score"], 1),
                "naturalness_score": round(naturalness_result["score"], 1)
            },
            "error_details": accuracy_result["error_details"],
            "timing_analysis": {
                "total_duration": round(fluency_result["total_duration"], 1),
                "speech_rate": round(fluency_result["speech_rate"], 1),
                "speech_rate_category": fluency_result["speech_rate_category"]
            },
            "voice_analysis": {
                "pitch_mean": round(naturalness_result["pitch_mean"], 1),
                "pitch_category": naturalness_result["pitch_category"]
            },
            "text_comparison": {
                "reference_text": reference_text,
                "recognized_text": recognized_text,
                "similarity": round(accuracy_result["similarity"], 1)
            }
        }
    
    def _generate_error_report(self, error_message: str) -> Dict[str, Any]:
        """生成错误报告"""
        return {
            "timestamp": datetime.now().isoformat(),
            "error": True,
            "error_message": error_message,
            "overall_score": 0,
            "grade_level": "评测失败"
        }

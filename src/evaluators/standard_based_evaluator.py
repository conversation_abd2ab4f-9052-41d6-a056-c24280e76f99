"""
基于标准音频的普通话评测器
使用标准音频作为基准进行更精确的评测
"""

import numpy as np
import librosa
from typing import Dict, List, Tuple, Any, Optional
import logging
from dtaidistance import dtw
import pickle
import os
from datetime import datetime

from .passage_evaluator import PassageEvaluator
from ..core.phoneme_analyzer import PhonemeAnalyzer

logger = logging.getLogger(__name__)


class StandardBasedEvaluator(PassageEvaluator):
    """基于标准音频的评测器"""
    
    def __init__(self):
        super().__init__()
        self.standard_audio_path = None
        self.standard_text = None
        self.standard_templates = None
        self.templates_cache_path = "standard_templates.pkl"
        
    def load_standard_audio(self, audio_path: str, text: str) -> bool:
        """
        加载标准音频并创建模板
        
        Args:
            audio_path: 标准音频文件路径
            text: 对应的标准文本
            
        Returns:
            是否加载成功
        """
        try:
            if not os.path.exists(audio_path):
                logger.error(f"标准音频文件不存在: {audio_path}")
                return False
            
            self.standard_audio_path = audio_path
            self.standard_text = text
            
            # 检查是否有缓存的模板
            cache_key = f"{audio_path}_{hash(text)}"
            cache_file = f"templates_{abs(hash(cache_key))}.pkl"
            
            if os.path.exists(cache_file):
                logger.info("加载缓存的标准音频模板")
                with open(cache_file, 'rb') as f:
                    self.standard_templates = pickle.load(f)
            else:
                logger.info("创建标准音频模板")
                self.standard_templates = self._create_standard_templates(audio_path, text)
                
                # 保存模板到缓存
                with open(cache_file, 'wb') as f:
                    pickle.dump(self.standard_templates, f)
                logger.info(f"标准音频模板已保存到: {cache_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"加载标准音频失败: {e}")
            return False
    
    def _create_standard_templates(self, audio_path: str, text: str) -> Dict[str, Any]:
        """创建标准音频模板"""
        try:
            # 1. 加载标准音频
            standard_audio, sr = self.audio_processor.load_audio(audio_path)
            processed_audio = self.audio_processor.preprocess_audio(standard_audio)
            
            # 2. 获取音素信息
            phoneme_info = self.phoneme_analyzer._get_pinyin_info(text)
            
            # 3. 音频分段对齐
            segments = self.phoneme_analyzer._simple_forced_alignment(processed_audio, phoneme_info)
            
            # 4. 为每个音素创建模板
            templates = {
                'global_features': self._extract_global_features(processed_audio),
                'phoneme_templates': [],
                'text': text,
                'total_duration': len(processed_audio) / self.audio_processor.sample_rate
            }
            
            for i, (segment, phoneme_data) in enumerate(zip(segments, phoneme_info)):
                if len(segment) > 100:  # 确保片段足够长
                    template = self._create_phoneme_template(segment, phoneme_data, i)
                    templates['phoneme_templates'].append(template)
            
            logger.info(f"创建了 {len(templates['phoneme_templates'])} 个音素模板")
            return templates
            
        except Exception as e:
            logger.error(f"创建标准音频模板失败: {e}")
            return {}
    
    def _extract_global_features(self, audio: np.ndarray) -> Dict[str, Any]:
        """提取全局特征"""
        try:
            # 整体MFCC特征
            mfcc = self.audio_processor.extract_mfcc_features(audio)
            
            # 整体基频特征
            f0 = self.audio_processor.extract_pitch(audio)
            f0_valid = f0[~np.isnan(f0)]
            
            # 能量特征
            energy = librosa.feature.rms(y=audio)[0]
            
            return {
                'mfcc_mean': np.mean(mfcc, axis=1),
                'mfcc_std': np.std(mfcc, axis=1),
                'f0_mean': np.mean(f0_valid) if len(f0_valid) > 0 else 0,
                'f0_std': np.std(f0_valid) if len(f0_valid) > 0 else 0,
                'f0_range': np.max(f0_valid) - np.min(f0_valid) if len(f0_valid) > 0 else 0,
                'energy_mean': np.mean(energy),
                'energy_std': np.std(energy)
            }
        except Exception as e:
            logger.error(f"提取全局特征失败: {e}")
            return {}
    
    def _create_phoneme_template(self, segment: np.ndarray, phoneme_data: Dict, index: int) -> Dict[str, Any]:
        """为单个音素创建模板"""
        try:
            template = {
                'index': index,
                'char': phoneme_data['char'],
                'pinyin': phoneme_data['pinyin'],
                'initial': phoneme_data['initial'],
                'final': phoneme_data['final'],
                'tone': phoneme_data['tone']
            }
            
            # 声母特征 (前1/3)
            initial_part = segment[:len(segment)//3]
            if len(initial_part) > 50:
                template['initial_features'] = self._extract_segment_features(initial_part, 'initial')
            
            # 韵母特征 (后2/3)
            final_part = segment[len(segment)//3:]
            if len(final_part) > 50:
                template['final_features'] = self._extract_segment_features(final_part, 'final')
            
            # 声调特征 (整个片段)
            template['tone_features'] = self._extract_segment_features(segment, 'tone')
            
            return template
            
        except Exception as e:
            logger.error(f"创建音素模板失败: {e}")
            return {}
    
    def _extract_segment_features(self, segment: np.ndarray, feature_type: str) -> Dict[str, Any]:
        """提取音频片段特征"""
        try:
            features = {}
            
            if feature_type in ['initial', 'final']:
                # MFCC特征
                mfcc = self.audio_processor.extract_mfcc_features(segment)
                features['mfcc_mean'] = np.mean(mfcc, axis=1)
                features['mfcc_std'] = np.std(mfcc, axis=1)
                features['mfcc_sequence'] = mfcc  # 保存完整序列用于DTW
            
            if feature_type in ['final', 'tone']:
                # 共振峰特征
                formants = self.phoneme_analyzer._extract_formants_simple(segment)
                features['formants'] = formants
            
            if feature_type == 'tone':
                # 基频轮廓
                f0 = self.pitch_extractor.extract_pitch(segment)
                f0_valid = f0[~np.isnan(f0)]
                if len(f0_valid) > 3:
                    features['f0_contour'] = f0_valid
                    features['f0_normalized'] = self._normalize_f0_contour(f0_valid)
            
            # 能量特征
            energy = librosa.feature.rms(y=segment)[0]
            features['energy_mean'] = np.mean(energy)
            features['energy_std'] = np.std(energy)
            
            return features
            
        except Exception as e:
            logger.error(f"提取片段特征失败: {e}")
            return {}
    
    def _normalize_f0_contour(self, f0_contour: np.ndarray) -> np.ndarray:
        """归一化基频轮廓"""
        if len(f0_contour) < 2:
            return f0_contour
        
        f0_min, f0_max = np.min(f0_contour), np.max(f0_contour)
        if f0_max - f0_min < 10:  # 变化太小
            return np.zeros_like(f0_contour)
        
        return (f0_contour - f0_min) / (f0_max - f0_min)
    
    def evaluate_with_standard(self, audio_file: str, reference_text: str) -> Dict[str, Any]:
        """
        基于标准音频进行评测
        
        Args:
            audio_file: 测试音频文件路径
            reference_text: 参考文本
            
        Returns:
            评测结果
        """
        if not self.standard_templates:
            logger.error("未加载标准音频模板")
            return self._generate_error_report("未加载标准音频模板")
        
        try:
            # 1. 音频预处理
            audio, sr = self.audio_processor.load_audio(audio_file)
            processed_audio = self.audio_processor.preprocess_audio(audio)
            
            # 2. 语音识别
            recognized_text = self._speech_recognition(audio_file)
            
            # 3. 基于标准模板的音素分析
            standard_result = self._analyze_with_standard_templates(processed_audio, reference_text)
            
            # 4. 流畅性分析
            fluency_result = self._analyze_fluency(processed_audio, reference_text)
            
            # 5. 综合评分
            overall_score = self._calculate_standard_based_score(standard_result, fluency_result)
            
            # 6. 生成报告
            evaluation_result = self._generate_standard_report(
                overall_score, standard_result, fluency_result, 
                recognized_text, reference_text
            )
            
            return evaluation_result
            
        except Exception as e:
            logger.error(f"基于标准音频的评测失败: {e}")
            return self._generate_error_report(str(e))
    
    def _analyze_with_standard_templates(self, audio: np.ndarray, text: str) -> Dict[str, Any]:
        """基于标准模板分析音频"""
        try:
            # 获取测试音频的音素信息
            phoneme_info = self.phoneme_analyzer._get_pinyin_info(text)
            
            # 音频分段
            segments = self.phoneme_analyzer._simple_forced_alignment(audio, phoneme_info)
            
            # 与标准模板对比
            phoneme_scores = []
            initial_scores = []
            final_scores = []
            tone_scores = []
            
            standard_templates = self.standard_templates['phoneme_templates']
            
            for i, (segment, phoneme_data) in enumerate(zip(segments, phoneme_info)):
                if len(segment) > 100 and i < len(standard_templates):
                    # 找到对应的标准模板
                    standard_template = standard_templates[i]
                    
                    # 分别评测声母、韵母、声调
                    scores = self._compare_with_template(segment, phoneme_data, standard_template)
                    
                    phoneme_scores.append(scores)
                    initial_scores.append(scores['initial_score'])
                    final_scores.append(scores['final_score'])
                    tone_scores.append(scores['tone_score'])
            
            return {
                'phoneme_details': phoneme_scores,
                'initial_accuracy': np.mean(initial_scores) if initial_scores else 0,
                'final_accuracy': np.mean(final_scores) if final_scores else 0,
                'tone_accuracy': np.mean(tone_scores) if tone_scores else 0,
                'total_phonemes': len(phoneme_scores)
            }
            
        except Exception as e:
            logger.error(f"基于标准模板分析失败: {e}")
            return {
                'phoneme_details': [],
                'initial_accuracy': 0,
                'final_accuracy': 0,
                'tone_accuracy': 0,
                'total_phonemes': 0
            }
    
    def _compare_with_template(self, segment: np.ndarray, phoneme_data: Dict, 
                              standard_template: Dict) -> Dict[str, Any]:
        """与标准模板对比"""
        try:
            scores = {
                'char': phoneme_data['char'],
                'pinyin': phoneme_data['pinyin'],
                'initial_score': 70.0,
                'final_score': 70.0,
                'tone_score': 70.0
            }
            
            # 声母对比
            if 'initial_features' in standard_template:
                initial_part = segment[:len(segment)//3]
                if len(initial_part) > 50:
                    scores['initial_score'] = self._compare_initial_features(
                        initial_part, standard_template['initial_features']
                    )
            
            # 韵母对比
            if 'final_features' in standard_template:
                final_part = segment[len(segment)//3:]
                if len(final_part) > 50:
                    scores['final_score'] = self._compare_final_features(
                        final_part, standard_template['final_features']
                    )
            
            # 声调对比
            if 'tone_features' in standard_template:
                scores['tone_score'] = self._compare_tone_features(
                    segment, standard_template['tone_features']
                )
            
            return scores
            
        except Exception as e:
            logger.error(f"模板对比失败: {e}")
            return {
                'char': phoneme_data.get('char', ''),
                'pinyin': phoneme_data.get('pinyin', ''),
                'initial_score': 50.0,
                'final_score': 50.0,
                'tone_score': 50.0
            }
    
    def _compare_initial_features(self, test_segment: np.ndarray, 
                                 standard_features: Dict) -> float:
        """对比声母特征"""
        try:
            # 提取测试音频的MFCC特征
            test_mfcc = self.audio_processor.extract_mfcc_features(test_segment)
            test_mfcc_mean = np.mean(test_mfcc, axis=1)
            
            # 与标准特征对比
            standard_mfcc_mean = standard_features['mfcc_mean']
            
            # 计算欧氏距离
            distance = np.linalg.norm(test_mfcc_mean - standard_mfcc_mean)
            
            # 转换为0-100分数 (距离越小分数越高)
            score = max(0, 100 - distance * 5)
            
            return min(100, score)
            
        except Exception as e:
            logger.error(f"声母特征对比失败: {e}")
            return 50.0
    
    def _compare_final_features(self, test_segment: np.ndarray, 
                               standard_features: Dict) -> float:
        """对比韵母特征"""
        try:
            # 提取共振峰特征
            test_formants = self.phoneme_analyzer._extract_formants_simple(test_segment)
            standard_formants = standard_features.get('formants', [0, 0, 0])
            
            # 计算共振峰差异
            formant_diff = 0
            valid_formants = 0
            
            for i in range(min(len(test_formants), len(standard_formants))):
                if test_formants[i] > 0 and standard_formants[i] > 0:
                    diff = abs(test_formants[i] - standard_formants[i]) / standard_formants[i]
                    formant_diff += diff
                    valid_formants += 1
            
            if valid_formants > 0:
                avg_diff = formant_diff / valid_formants
                score = max(0, 100 - avg_diff * 100)
            else:
                score = 70.0
            
            return min(100, score)
            
        except Exception as e:
            logger.error(f"韵母特征对比失败: {e}")
            return 50.0
    
    def _compare_tone_features(self, test_segment: np.ndarray, 
                              standard_features: Dict) -> float:
        """对比声调特征"""
        try:
            # 提取基频轮廓
            test_f0 = self.pitch_extractor.extract_pitch(test_segment)
            test_f0_valid = test_f0[~np.isnan(test_f0)]
            
            if len(test_f0_valid) < 3:
                return 50.0
            
            # 归一化
            test_f0_norm = self._normalize_f0_contour(test_f0_valid)
            standard_f0_norm = standard_features.get('f0_normalized', np.array([]))
            
            if len(standard_f0_norm) < 3:
                return 70.0
            
            # 使用DTW计算轮廓相似度
            try:
                distance = dtw.distance(test_f0_norm, standard_f0_norm)
                score = max(0, 100 - distance * 50)
            except:
                # DTW失败时使用相关系数
                min_len = min(len(test_f0_norm), len(standard_f0_norm))
                if min_len > 2:
                    correlation = np.corrcoef(
                        test_f0_norm[:min_len], 
                        standard_f0_norm[:min_len]
                    )[0, 1]
                    score = max(0, correlation * 100) if not np.isnan(correlation) else 50
                else:
                    score = 50
            
            return min(100, score)
            
        except Exception as e:
            logger.error(f"声调特征对比失败: {e}")
            return 50.0
    
    def _calculate_standard_based_score(self, standard_result: Dict, fluency_result: Dict) -> float:
        """计算基于标准音频的综合得分"""
        overall_score = (
            standard_result["initial_accuracy"] * self.weights["initial"] +
            standard_result["final_accuracy"] * self.weights["final"] +
            standard_result["tone_accuracy"] * self.weights["tone"] +
            fluency_result["score"] * self.weights["fluency"]
        )
        return round(overall_score, 1)
    
    def _generate_standard_report(self, overall_score: float, standard_result: Dict,
                                 fluency_result: Dict, recognized_text: str, 
                                 reference_text: str) -> Dict[str, Any]:
        """生成基于标准音频的评测报告"""
        grade_level = self._get_grade_level(overall_score)
        
        return {
            "timestamp": datetime.now().isoformat(),
            "evaluation_type": "standard_based",
            "standard_audio_path": self.standard_audio_path,
            "overall_score": overall_score,
            "grade_level": grade_level,
            "detailed_analysis": {
                "initial_accuracy": round(standard_result["initial_accuracy"], 1),
                "final_accuracy": round(standard_result["final_accuracy"], 1),
                "tone_accuracy": round(standard_result["tone_accuracy"], 1),
                "fluency_score": round(fluency_result["score"], 1)
            },
            "phoneme_details": {
                "total_phonemes": standard_result["total_phonemes"],
                "phoneme_scores": standard_result["phoneme_details"]
            },
            "timing_analysis": {
                "total_duration": round(fluency_result["total_duration"], 1),
                "speech_rate": round(fluency_result["speech_rate"], 1),
                "speech_rate_category": fluency_result["speech_rate_category"]
            },
            "text_comparison": {
                "reference_text": reference_text,
                "recognized_text": recognized_text
            }
        }

"""
音素分析器
实现声母、韵母、声调的识别和分析
"""

import numpy as np
import librosa
from typing import Dict, List, Tuple, Any
from pypinyin import lazy_pinyin, Style
import jieba
from dtaidistance import dtw
import logging
import warnings

# 屏蔽librosa的警告
warnings.filterwarnings("ignore", message="n_fft=.* is too large for input signal of length=.*")
warnings.filterwarnings("ignore", message="With fmin=.*, sr=.* and frame_length=.*, less than two periods.*")
warnings.filterwarnings("ignore", message="fmin=.* is too small for frame_length=.* and sr=.*")
warnings.filterwarnings("ignore", message="Empty filters detected in mel frequency basis.*")

logger = logging.getLogger(__name__)


class PhonemeAnalyzer:
    """音素分析器"""
    
    def __init__(self, sample_rate: int = 16000):
        self.sample_rate = sample_rate
        
        # 声母分类
        self.initials = {
            'b': ['b'], 'p': ['p'], 'm': ['m'], 'f': ['f'],
            'd': ['d'], 't': ['t'], 'n': ['n'], 'l': ['l'],
            'g': ['g'], 'k': ['k'], 'h': ['h'],
            'j': ['j'], 'q': ['q'], 'x': ['x'],
            'zh': ['zh'], 'ch': ['ch'], 'sh': ['sh'], 'r': ['r'],
            'z': ['z'], 'c': ['c'], 's': ['s'],
            'y': ['y'], 'w': ['w'], '': ['']  # 零声母
        }
        
        # 韵母分类
        self.finals = {
            'a': ['a'], 'o': ['o'], 'e': ['e'], 'i': ['i'], 'u': ['u'], 'v': ['ü'],
            'ai': ['ai'], 'ei': ['ei'], 'ui': ['ui'], 'ao': ['ao'], 'ou': ['ou'], 'iu': ['iu'],
            'ie': ['ie'], 've': ['üe'], 'er': ['er'],
            'an': ['an'], 'en': ['en'], 'in': ['in'], 'un': ['un'], 'vn': ['ün'],
            'ang': ['ang'], 'eng': ['eng'], 'ing': ['ing'], 'ong': ['ong']
        }
        
        # 声调模式 (基频轮廓特征)
        self.tone_patterns = {
            1: 'high_flat',      # 一声：高平
            2: 'rising',         # 二声：上升
            3: 'dipping',        # 三声：曲折
            4: 'falling',        # 四声：下降
            0: 'neutral'         # 轻声
        }
    
    def analyze_phonemes(self, audio: np.ndarray, text: str) -> Dict[str, Any]:
        """
        分析音素

        Args:
            audio: 音频数据
            text: 对应文本

        Returns:
            音素分析结果
        """
        try:
            # 0. 音频质量检测
            quality_score = self._assess_audio_quality(audio)
            if quality_score < 50:
                logger.warning(f"音频质量较差: {quality_score}")

            # 1. 获取文本的拼音信息
            pinyin_info = self._get_pinyin_info(text)

            # 2. 音频分段 (改进版强制对齐)
            segments = self._simple_forced_alignment(audio, pinyin_info)

            # 3. 分析每个音素
            phoneme_results = []

            for i, (segment_audio, pinyin_data) in enumerate(zip(segments, pinyin_info)):
                if len(segment_audio) > 0:
                    result = self._analyze_single_phoneme(segment_audio, pinyin_data, i)
                    phoneme_results.append(result)

            # 4. 统计分析
            summary = self._summarize_phoneme_analysis(phoneme_results)
            summary['audio_quality'] = quality_score

            return {
                'phoneme_details': phoneme_results,
                'summary': summary
            }

        except Exception as e:
            logger.error(f"音素分析失败: {e}")
            return {
                'phoneme_details': [],
                'summary': {
                    'initial_accuracy': 0,
                    'final_accuracy': 0,
                    'tone_accuracy': 0,
                    'total_phonemes': 0,
                    'audio_quality': 0
                }
            }

    def _assess_audio_quality(self, audio: np.ndarray) -> float:
        """评估音频质量"""
        quality_score = 100.0

        # 1. 信噪比估计
        # 使用前后静音段估计噪声
        silence_threshold = 0.01
        noise_samples = audio[np.abs(audio) < silence_threshold]
        if len(noise_samples) > 100:
            noise_level = np.std(noise_samples)
            signal_level = np.std(audio)
            if signal_level > 0:
                snr = 20 * np.log10(signal_level / (noise_level + 1e-10))
                if snr < 10:  # SNR太低
                    quality_score -= 30
                elif snr < 20:
                    quality_score -= 15

        # 2. 动态范围检测
        dynamic_range = np.max(audio) - np.min(audio)
        if dynamic_range < 0.1:  # 动态范围太小
            quality_score -= 20
        elif dynamic_range > 1.8:  # 可能有削波
            quality_score -= 15

        # 3. 频谱完整性
        fft = np.fft.fft(audio)
        freq_magnitude = np.abs(fft[:len(fft)//2])

        # 检查高频成分
        high_freq_energy = np.sum(freq_magnitude[len(freq_magnitude)//2:])
        total_energy = np.sum(freq_magnitude)
        if high_freq_energy / total_energy < 0.1:  # 高频成分太少
            quality_score -= 10

        # 4. 削波检测
        clipping_ratio = np.sum(np.abs(audio) > 0.95) / len(audio)
        if clipping_ratio > 0.01:  # 超过1%的样本削波
            quality_score -= 25

        return max(0, quality_score)

    def _get_adaptive_fft_params(self, audio_length: int) -> Tuple[int, int]:
        """根据音频长度自适应调整FFT参数"""
        if audio_length < 512:
            n_fft = min(256, audio_length)
            hop_length = n_fft // 4
        elif audio_length < 1024:
            n_fft = 512
            hop_length = 128
        elif audio_length < 2048:
            n_fft = 1024
            hop_length = 256
        else:
            n_fft = 2048
            hop_length = 512

        return n_fft, hop_length

    def _get_adaptive_pitch_params(self, audio_length: int) -> Tuple[int, int, float]:
        """根据音频长度自适应调整基频提取参数"""
        # 确保frame_length能容纳至少2个fmin周期
        # 对于fmin=50Hz, sr=16000, 需要frame_length >= 640
        # 对于fmin=80Hz, sr=16000, 需要frame_length >= 400

        if audio_length < 400:
            # 音频太短，使用更高的fmin
            frame_length = max(256, audio_length)
            fmin = max(100, 16000 * 2 / frame_length)  # 确保至少2个周期
            hop_length = frame_length // 4
        elif audio_length < 640:
            frame_length = 400
            fmin = 80  # 80Hz对应400帧长度
            hop_length = 100
        elif audio_length < 1280:
            frame_length = 640
            fmin = 50  # 50Hz对应640帧长度
            hop_length = 160
        else:
            frame_length = 1024
            fmin = 50
            hop_length = 256

        return frame_length, hop_length, fmin
    
    def _get_pinyin_info(self, text: str) -> List[Dict[str, Any]]:
        """获取文本的拼音信息"""
        # 清理文本，只保留中文字符
        import re
        clean_text = re.sub(r'[^\u4e00-\u9fff]', '', text)
        
        pinyin_info = []
        
        # 获取拼音
        pinyins = lazy_pinyin(clean_text, style=Style.TONE3)  # 带声调数字
        chars = list(clean_text)
        
        for char, pinyin in zip(chars, pinyins):
            # 解析拼音
            initial, final, tone = self._parse_pinyin(pinyin)
            
            pinyin_info.append({
                'char': char,
                'pinyin': pinyin,
                'initial': initial,
                'final': final,
                'tone': tone
            })
        
        return pinyin_info
    
    def _parse_pinyin(self, pinyin: str) -> Tuple[str, str, int]:
        """解析拼音为声母、韵母、声调"""
        # 提取声调
        tone = 0
        if pinyin[-1].isdigit():
            tone = int(pinyin[-1])
            pinyin_without_tone = pinyin[:-1]
        else:
            pinyin_without_tone = pinyin
        
        # 分离声母和韵母
        initial = ""
        final = pinyin_without_tone
        
        # 声母匹配 (按长度从长到短)
        for init in ['zh', 'ch', 'sh']:
            if pinyin_without_tone.startswith(init):
                initial = init
                final = pinyin_without_tone[len(init):]
                break
        
        if not initial:
            for init in ['b', 'p', 'm', 'f', 'd', 't', 'n', 'l', 'g', 'k', 'h', 
                        'j', 'q', 'x', 'r', 'z', 'c', 's', 'y', 'w']:
                if pinyin_without_tone.startswith(init):
                    initial = init
                    final = pinyin_without_tone[len(init):]
                    break
        
        return initial, final, tone
    
    def _simple_forced_alignment(self, audio: np.ndarray, pinyin_info: List[Dict]) -> List[np.ndarray]:
        """改进的基于规则的强制对齐"""
        total_chars = len(pinyin_info)
        if total_chars == 0:
            return []

        # 1. 语音活动检测，找到有效语音段
        voice_segments = self._detect_voice_segments(audio)

        # 2. 基于能量变化检测可能的音素边界
        boundaries = self._detect_phoneme_boundaries(audio, total_chars)

        # 3. 根据边界分割音频
        segments = []
        for i in range(total_chars):
            if i < len(boundaries) - 1:
                start_idx = boundaries[i]
                end_idx = boundaries[i + 1]
            else:
                start_idx = boundaries[i] if i < len(boundaries) else len(audio) // 2
                end_idx = len(audio)

            segments.append(audio[start_idx:end_idx])

        return segments

    def _detect_voice_segments(self, audio: np.ndarray) -> List[Tuple[int, int]]:
        """检测语音活动段"""
        # 计算短时能量
        frame_length = int(0.025 * self.sample_rate)  # 25ms
        hop_length = int(0.01 * self.sample_rate)     # 10ms

        energy = []
        for i in range(0, len(audio) - frame_length, hop_length):
            frame = audio[i:i + frame_length]
            energy.append(np.sum(frame ** 2))

        # 能量阈值检测
        energy = np.array(energy)
        threshold = np.mean(energy) * 0.1  # 动态阈值

        voice_segments = []
        in_voice = False
        start_idx = 0

        for i, e in enumerate(energy):
            if e > threshold and not in_voice:
                start_idx = i * hop_length
                in_voice = True
            elif e <= threshold and in_voice:
                end_idx = i * hop_length
                voice_segments.append((start_idx, end_idx))
                in_voice = False

        if in_voice:
            voice_segments.append((start_idx, len(audio)))

        return voice_segments

    def _detect_phoneme_boundaries(self, audio: np.ndarray, num_phonemes: int) -> List[int]:
        """基于声学特征检测音素边界"""
        # 计算频谱质心变化
        hop_length = int(0.01 * self.sample_rate)  # 10ms
        spectral_centroids = librosa.feature.spectral_centroid(
            y=audio, sr=self.sample_rate, hop_length=hop_length
        )[0]

        # 动态调整MFCC参数
        n_fft = min(2048, len(audio))
        if n_fft < 512:
            n_fft = 512
        hop_length = min(hop_length, n_fft // 4)

        # 调整梅尔滤波器参数
        n_mels = min(128, n_fft // 4)
        if n_mels < 40:
            n_mels = 40
        actual_n_mfcc = min(13, n_mels // 2)
        if actual_n_mfcc < 13:
            actual_n_mfcc = min(13, n_mels // 3)

        # 计算MFCC变化
        mfcc = librosa.feature.mfcc(
            y=audio,
            sr=self.sample_rate,
            n_mfcc=actual_n_mfcc,
            n_mels=n_mels,
            n_fft=n_fft,
            hop_length=hop_length,
            fmax=self.sample_rate // 2
        )

        # 计算特征变化率
        centroid_diff = np.abs(np.diff(spectral_centroids))
        mfcc_diff = np.mean(np.abs(np.diff(mfcc, axis=1)), axis=0)

        # 综合变化指标
        change_score = centroid_diff + mfcc_diff

        # 找到变化最大的点作为边界
        if len(change_score) < num_phonemes:
            # 如果特征点不够，回退到等时长分割
            boundaries = [i * len(audio) // num_phonemes for i in range(num_phonemes + 1)]
        else:
            # 找到变化最大的num_phonemes-1个点
            peak_indices = np.argsort(change_score)[-num_phonemes+1:]
            peak_indices = np.sort(peak_indices) * hop_length
            boundaries = [0] + peak_indices.tolist() + [len(audio)]

        return boundaries
    
    def _analyze_single_phoneme(self, segment_audio: np.ndarray, pinyin_data: Dict, index: int) -> Dict[str, Any]:
        """分析单个音素"""
        try:
            # 声母分析
            initial_score = self._analyze_initial(segment_audio, pinyin_data['initial'])
            
            # 韵母分析
            final_score = self._analyze_final(segment_audio, pinyin_data['final'])
            
            # 声调分析
            tone_score = self._analyze_tone(segment_audio, pinyin_data['tone'])
            
            return {
                'index': index,
                'char': pinyin_data['char'],
                'pinyin': pinyin_data['pinyin'],
                'initial': {
                    'expected': pinyin_data['initial'],
                    'score': initial_score,
                    'category': self._get_initial_category(pinyin_data['initial'])
                },
                'final': {
                    'expected': pinyin_data['final'],
                    'score': final_score,
                    'category': self._get_final_category(pinyin_data['final'])
                },
                'tone': {
                    'expected': pinyin_data['tone'],
                    'score': tone_score,
                    'pattern': self.tone_patterns.get(pinyin_data['tone'], 'unknown')
                }
            }
            
        except Exception as e:
            logger.error(f"单个音素分析失败: {e}")
            return {
                'index': index,
                'char': pinyin_data['char'],
                'pinyin': pinyin_data['pinyin'],
                'initial': {'expected': pinyin_data['initial'], 'score': 0, 'category': 'unknown'},
                'final': {'expected': pinyin_data['final'], 'score': 0, 'category': 'unknown'},
                'tone': {'expected': pinyin_data['tone'], 'score': 0, 'pattern': 'unknown'}
            }
    
    def _analyze_initial(self, audio: np.ndarray, expected_initial: str) -> float:
        """分析声母"""
        if len(audio) < 100:  # 音频太短
            return 50.0

        try:
            # 提取前1/3部分的音频 (声母通常在开头)
            initial_part = audio[:len(audio)//3]

            # 自适应调整FFT参数
            n_fft, hop_length = self._get_adaptive_fft_params(len(initial_part))

            # 确保音频长度足够
            if len(initial_part) < n_fft:
                initial_part = np.pad(initial_part, (0, n_fft - len(initial_part)), 'constant')

            # 根据n_fft调整MFCC参数
            n_mels = min(128, n_fft // 4)
            if n_mels < 40:
                n_mels = 40
            actual_n_mfcc = min(13, n_mels // 2)
            if actual_n_mfcc < 13:
                actual_n_mfcc = min(13, n_mels // 3)

            # 提取MFCC特征
            mfcc = librosa.feature.mfcc(
                y=initial_part,
                sr=self.sample_rate,
                n_mfcc=actual_n_mfcc,
                n_mels=n_mels,
                n_fft=n_fft,
                hop_length=hop_length,
                fmax=self.sample_rate // 2
            )

            # 计算特征统计量
            mfcc_mean = np.mean(mfcc, axis=1)
            mfcc_std = np.std(mfcc, axis=1)

            # 基于声母类型的评分
            score = self._score_initial_features(mfcc_mean, mfcc_std, expected_initial)

            return min(100.0, max(0.0, score))

        except Exception as e:
            logger.error(f"声母分析失败: {e}")
            return 50.0
    
    def _analyze_final(self, audio: np.ndarray, expected_final: str) -> float:
        """分析韵母"""
        if len(audio) < 100:
            return 50.0
        
        try:
            # 提取后2/3部分的音频 (韵母通常在后面)
            final_part = audio[len(audio)//3:]
            
            # 提取共振峰特征
            formants = self._extract_formants_simple(final_part)
            
            # 基于韵母类型的评分
            score = self._score_final_features(formants, expected_final)
            
            return min(100.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"韵母分析失败: {e}")
            return 50.0
    
    def _analyze_tone(self, audio: np.ndarray, expected_tone: int) -> float:
        """分析声调"""
        if len(audio) < 100:
            return 50.0

        try:
            # 自适应调整基频提取参数
            frame_length, hop_length, fmin = self._get_adaptive_pitch_params(len(audio))

            # 确保音频长度足够
            if len(audio) < frame_length:
                audio = np.pad(audio, (0, frame_length - len(audio)), 'constant')

            # 提取基频
            f0, voiced_flag, voiced_probs = librosa.pyin(
                audio,
                fmin=fmin,
                fmax=500,
                sr=self.sample_rate,
                frame_length=frame_length,
                hop_length=hop_length
            )

            # 过滤有效基频
            valid_f0 = f0[voiced_flag & ~np.isnan(f0)]

            if len(valid_f0) < 3:
                return 50.0

            # 分析基频轮廓
            score = self._score_tone_contour(valid_f0, expected_tone)

            return min(100.0, max(0.0, score))

        except Exception as e:
            logger.error(f"声调分析失败: {e}")
            return 50.0
    
    def _score_initial_features(self, mfcc_mean: np.ndarray, mfcc_std: np.ndarray, expected_initial: str) -> float:
        """改进的基于MFCC特征评分声母"""
        base_score = 70.0

        # 更详细的声母分类和特征分析
        if expected_initial in ['b', 'p']:  # 双唇爆破音
            # 爆破音特征：起始能量突变，低频成分强
            if mfcc_mean[0] < -8 and mfcc_std[0] > 2:  # 低频能量强且变化大
                base_score += 15
            # 区分清浊音
            if expected_initial == 'p' and mfcc_mean[1] > 0:  # 清音高频成分多
                base_score += 5
            elif expected_initial == 'b' and mfcc_mean[1] < 0:  # 浊音低频成分多
                base_score += 5

        elif expected_initial == 'm':  # 双唇鼻音
            # 鼻音特征：低频共振峰明显
            if mfcc_mean[0] < -5 and mfcc_std[1] < 3:  # 低频强，中频稳定
                base_score += 15

        elif expected_initial == 'f':  # 唇齿擦音
            # 擦音特征：高频噪声成分
            if mfcc_mean[2] > 1 and mfcc_std[2] > 2:  # 高频成分强且变化大
                base_score += 15

        elif expected_initial in ['d', 't']:  # 舌尖爆破音
            # 舌尖音特征：中频突出
            if -3 < mfcc_mean[1] < 3 and mfcc_std[1] > 1.5:
                base_score += 15
            # 清浊音区分
            if expected_initial == 't' and mfcc_mean[2] > 0:
                base_score += 5
            elif expected_initial == 'd' and mfcc_mean[2] < 0:
                base_score += 5

        elif expected_initial in ['n', 'l']:  # 舌尖鼻音和边音
            # 连续音特征：频谱相对稳定
            if np.mean(mfcc_std) < 2:  # 整体变化小
                base_score += 15
            if expected_initial == 'l' and mfcc_mean[1] > -2:  # 边音中频特征
                base_score += 5

        elif expected_initial in ['zh', 'ch', 'sh']:  # 舌尖后音
            # 卷舌音特征：第三共振峰下降
            if mfcc_mean[2] < -1 and mfcc_mean[3] < 0:
                base_score += 15
            # 区分爆破音和擦音
            if expected_initial in ['zh', 'ch'] and mfcc_std[0] > 2:  # 爆破音能量变化大
                base_score += 5
            elif expected_initial == 'sh' and mfcc_std[2] > 2:  # 擦音高频变化大
                base_score += 5

        elif expected_initial == 'r':  # 舌尖后近音
            # 近音特征：类似元音但F3较低
            if mfcc_mean[2] < -0.5 and np.mean(mfcc_std) < 2.5:
                base_score += 15

        elif expected_initial in ['j', 'q', 'x']:  # 舌面音
            # 舌面音特征：F2较高
            if mfcc_mean[1] > 0 and mfcc_mean[2] > -0.5:
                base_score += 15

        elif expected_initial in ['g', 'k', 'h']:  # 舌根音
            # 舌根音特征：F2较低
            if mfcc_mean[1] < -1 and mfcc_mean[0] < -5:
                base_score += 15

        elif expected_initial in ['z', 'c', 's']:  # 舌尖前音
            # 舌尖前音特征：高频噪声但比sh频率低
            if mfcc_mean[2] > 0 and mfcc_mean[3] < 0:
                base_score += 15

        elif expected_initial == '':  # 零声母
            # 零声母特征：起始较平缓
            if mfcc_std[0] < 2:  # 起始能量变化小
                base_score += 15

        return min(100.0, base_score)
    
    def _extract_formants_simple(self, audio: np.ndarray) -> List[float]:
        """简单的共振峰提取"""
        try:
            # 使用LPC分析
            from scipy.signal import lfilter
            
            # 预加重
            pre_emphasis = 0.97
            emphasized_audio = np.append(audio[0], audio[1:] - pre_emphasis * audio[:-1])
            
            # 窗函数
            windowed = emphasized_audio * np.hamming(len(emphasized_audio))
            
            # 自相关
            autocorr = np.correlate(windowed, windowed, mode='full')
            autocorr = autocorr[len(autocorr)//2:]
            
            # 简化的共振峰估计
            formants = []
            if len(autocorr) > 10:
                # 找峰值作为共振峰估计
                peaks = []
                for i in range(1, min(len(autocorr)-1, 100)):
                    if autocorr[i] > autocorr[i-1] and autocorr[i] > autocorr[i+1]:
                        freq = i * self.sample_rate / len(autocorr)
                        if 200 < freq < 4000:  # 合理的共振峰范围
                            peaks.append(freq)
                
                formants = sorted(peaks)[:3]  # 取前3个共振峰
            
            # 填充到3个共振峰
            while len(formants) < 3:
                formants.append(0)
            
            return formants[:3]
            
        except Exception as e:
            logger.error(f"共振峰提取失败: {e}")
            return [0, 0, 0]
    
    def _score_final_features(self, formants: List[float], expected_final: str) -> float:
        """改进的基于共振峰特征评分韵母"""
        base_score = 70.0

        if len(formants) < 2 or formants[0] == 0:
            return base_score

        f1, f2 = formants[0], formants[1]
        f3 = formants[2] if len(formants) > 2 and formants[2] > 0 else 2500

        # 更精确的韵母共振峰标准
        if expected_final == 'a':  # 低央元音
            if 650 < f1 < 850 and 1100 < f2 < 1400:
                base_score += 20
        elif expected_final == 'o':  # 半低后元音
            if 450 < f1 < 650 and 800 < f2 < 1100:
                base_score += 20
        elif expected_final == 'e':  # 半低前元音
            if 500 < f1 < 700 and 1400 < f2 < 1800:
                base_score += 20
        elif expected_final == 'i':  # 高前元音
            if f1 < 350 and f2 > 2000:
                base_score += 20
        elif expected_final == 'u':  # 高后元音
            if f1 < 350 and f2 < 1000:
                base_score += 20
        elif expected_final == 'v':  # 高前圆唇元音
            if f1 < 350 and 1200 < f2 < 1800:
                base_score += 20

        # 复韵母分析
        elif expected_final == 'ai':  # a->i滑动
            if 600 < f1 < 800 and f2 > 1500:  # 兼具a和i特征
                base_score += 18
        elif expected_final == 'ei':  # e->i滑动
            if 400 < f1 < 600 and f2 > 1800:
                base_score += 18
        elif expected_final == 'ao':  # a->o滑动
            if 600 < f1 < 800 and 1000 < f2 < 1300:
                base_score += 18
        elif expected_final == 'ou':  # o->u滑动
            if 400 < f1 < 600 and f2 < 1200:
                base_score += 18

        # 鼻韵母分析
        elif expected_final in ['an', 'en', 'in', 'un']:  # 前鼻韵母
            # 鼻音特征：鼻音共振峰
            if f1 > 200 and 1000 < f2 < 1500:  # 鼻音共振特征
                base_score += 15
            # 根据主元音调整
            if expected_final == 'an' and f1 > 600:
                base_score += 5
            elif expected_final == 'en' and 400 < f1 < 600:
                base_score += 5
            elif expected_final == 'in' and f1 < 400:
                base_score += 5
            elif expected_final == 'un' and f1 < 400 and f2 < 1200:
                base_score += 5

        elif expected_final in ['ang', 'eng', 'ing', 'ong']:  # 后鼻韵母
            # 后鼻音特征：更低的共振峰
            if f1 > 200 and f2 < 1800:
                base_score += 15
            # 根据主元音调整
            if expected_final == 'ang' and f1 > 600:
                base_score += 5
            elif expected_final == 'eng' and 400 < f1 < 600:
                base_score += 5
            elif expected_final == 'ing' and f1 < 400 and f2 > 1800:
                base_score += 5
            elif expected_final == 'ong' and f1 < 500 and f2 < 1000:
                base_score += 5

        # 特殊韵母
        elif expected_final == 'er':  # 儿化音
            # 儿化特征：F3明显下降
            if f3 < 2000 and f2 < 1500:
                base_score += 20

        return min(100.0, base_score)
    
    def _score_tone_contour(self, f0_contour: np.ndarray, expected_tone: int) -> float:
        """改进的基于基频轮廓评分声调"""
        if len(f0_contour) < 5:
            return 50.0

        # 平滑基频轮廓
        from scipy.ndimage import gaussian_filter1d
        f0_smooth = gaussian_filter1d(f0_contour, sigma=1)

        # 归一化到0-1范围
        f0_min, f0_max = np.min(f0_smooth), np.max(f0_smooth)
        if f0_max - f0_min < 10:  # 变化太小
            return 60.0
        f0_norm = (f0_smooth - f0_min) / (f0_max - f0_min)

        base_score = 65.0

        # 计算轮廓特征
        start_val = np.mean(f0_norm[:len(f0_norm)//4])  # 起始1/4
        mid_val = np.mean(f0_norm[len(f0_norm)//3:2*len(f0_norm)//3])  # 中间1/3
        end_val = np.mean(f0_norm[3*len(f0_norm)//4:])  # 结尾1/4

        # 计算斜率
        overall_slope = (end_val - start_val) / len(f0_norm)
        first_half_slope = (mid_val - start_val) / (len(f0_norm) // 2)
        second_half_slope = (end_val - mid_val) / (len(f0_norm) // 2)

        if expected_tone == 1:  # 一声：高平调 (55)
            # 特征：整体平稳，变化小，音高相对较高
            stability = 1 - np.std(f0_norm)  # 稳定性
            if stability > 0.85:  # 非常平稳
                base_score += 25
            elif stability > 0.7:  # 较平稳
                base_score += 15
            # 音高应该相对较高
            if np.mean(f0_norm) > 0.6:
                base_score += 5

        elif expected_tone == 2:  # 二声：中升调 (35)
            # 特征：明显上升，起点中等，终点高
            if overall_slope > 0.02:  # 整体上升
                base_score += 20
            if start_val < 0.5 and end_val > 0.7:  # 从中等升到高
                base_score += 10
            # 上升应该比较均匀
            if first_half_slope > 0 and second_half_slope > 0:
                base_score += 5

        elif expected_tone == 3:  # 三声：低降升调 (214)
            # 特征：先降后升，最低点在中后部
            min_idx = np.argmin(f0_norm)
            min_position = min_idx / len(f0_norm)

            if 0.4 < min_position < 0.8:  # 最低点在中后部
                base_score += 15
            if start_val > mid_val and end_val > mid_val:  # 两端高，中间低
                base_score += 15
            # 检查是否有明显的降升过程
            if first_half_slope < -0.01 and second_half_slope > 0.01:
                base_score += 10

        elif expected_tone == 4:  # 四声：高降调 (51)
            # 特征：明显下降，起点高，终点低
            if overall_slope < -0.02:  # 整体下降
                base_score += 20
            if start_val > 0.7 and end_val < 0.4:  # 从高降到低
                base_score += 10
            # 下降应该比较明显且持续
            if first_half_slope < 0 and second_half_slope < 0:
                base_score += 5

        elif expected_tone == 0:  # 轻声
            # 特征：音高较低，变化相对平缓
            if np.mean(f0_norm) < 0.4:  # 整体音高低
                base_score += 15
            if np.std(f0_norm) < 0.3:  # 变化不大
                base_score += 10

        return min(100.0, base_score)
    
    def _get_initial_category(self, initial: str) -> str:
        """获取声母类别"""
        if initial in ['b', 'p', 'm', 'f']:
            return '唇音'
        elif initial in ['d', 't', 'n', 'l']:
            return '舌尖音'
        elif initial in ['g', 'k', 'h']:
            return '舌根音'
        elif initial in ['j', 'q', 'x']:
            return '舌面音'
        elif initial in ['zh', 'ch', 'sh', 'r']:
            return '舌尖后音'
        elif initial in ['z', 'c', 's']:
            return '舌尖前音'
        else:
            return '其他'
    
    def _get_final_category(self, final: str) -> str:
        """获取韵母类别"""
        if final in ['a', 'o', 'e', 'i', 'u', 'v']:
            return '单韵母'
        elif final in ['ai', 'ei', 'ui', 'ao', 'ou', 'iu', 'ie', 've', 'er']:
            return '复韵母'
        elif final in ['an', 'en', 'in', 'un', 'vn']:
            return '前鼻韵母'
        elif final in ['ang', 'eng', 'ing', 'ong']:
            return '后鼻韵母'
        else:
            return '其他'
    
    def _summarize_phoneme_analysis(self, phoneme_results: List[Dict]) -> Dict[str, Any]:
        """汇总音素分析结果"""
        if not phoneme_results:
            return {
                'initial_accuracy': 0,
                'final_accuracy': 0,
                'tone_accuracy': 0,
                'total_phonemes': 0,
                'initial_errors': [],
                'final_errors': [],
                'tone_errors': []
            }
        
        initial_scores = [p['initial']['score'] for p in phoneme_results]
        final_scores = [p['final']['score'] for p in phoneme_results]
        tone_scores = [p['tone']['score'] for p in phoneme_results]
        
        # 找出低分项目
        initial_errors = [p for p in phoneme_results if p['initial']['score'] < 70]
        final_errors = [p for p in phoneme_results if p['final']['score'] < 70]
        tone_errors = [p for p in phoneme_results if p['tone']['score'] < 70]
        
        return {
            'initial_accuracy': np.mean(initial_scores),
            'final_accuracy': np.mean(final_scores),
            'tone_accuracy': np.mean(tone_scores),
            'total_phonemes': len(phoneme_results),
            'initial_errors': initial_errors[:5],  # 最多显示5个错误
            'final_errors': final_errors[:5],
            'tone_errors': tone_errors[:5]
        }

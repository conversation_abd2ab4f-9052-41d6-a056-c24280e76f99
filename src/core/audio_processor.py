"""
语音处理核心模块
负责音频的预处理、特征提取和基础分析
"""

import librosa
import numpy as np
import soundfile as sf
import webrtcvad
from typing import Tuple, List, Optional
import logging
from scipy import signal
from scipy.signal import butter, filtfilt
import warnings

# 屏蔽librosa的警告
warnings.filterwarnings("ignore", message="n_fft=.* is too large for input signal of length=.*")
warnings.filterwarnings("ignore", message="With fmin=.*, sr=.* and frame_length=.*, less than two periods.*")
warnings.filterwarnings("ignore", message="fmin=.* is too small for frame_length=.* and sr=.*")
warnings.filterwarnings("ignore", message="Empty filters detected in mel frequency basis.*")

logger = logging.getLogger(__name__)


class AudioProcessor:
    """音频处理器"""
    
    def __init__(self, sample_rate: int = 16000):
        self.sample_rate = sample_rate
        self.vad = webrtcvad.Vad(3)  # 最高敏感度的VAD
        self.frame_duration = 30  # ms
        self.frame_size = int(sample_rate * self.frame_duration / 1000)
        
    def load_audio(self, file_path: str) -> Tuple[np.ndarray, int]:
        """加载音频文件"""
        try:
            audio, sr = librosa.load(file_path, sr=self.sample_rate)
            return audio, sr
        except Exception as e:
            logger.error(f"音频加载失败: {e}")
            raise
    
    def preprocess_audio(self, audio: np.ndarray) -> np.ndarray:
        """音频预处理"""
        # 1. 归一化
        audio = self._normalize_audio(audio)
        
        # 2. 降噪
        audio = self._denoise_audio(audio)
        
        # 3. 预加重
        audio = self._preemphasis(audio)
        
        return audio
    
    def _normalize_audio(self, audio: np.ndarray) -> np.ndarray:
        """音频归一化"""
        max_val = np.max(np.abs(audio))
        if max_val > 0:
            audio = audio / max_val * 0.95
        return audio
    
    def _denoise_audio(self, audio: np.ndarray) -> np.ndarray:
        """简单降噪处理"""
        # 使用高通滤波器去除低频噪声
        nyquist = self.sample_rate / 2
        low_cutoff = 80 / nyquist
        b, a = butter(4, low_cutoff, btype='high')
        filtered_audio = filtfilt(b, a, audio)
        return filtered_audio
    
    def _preemphasis(self, audio: np.ndarray, alpha: float = 0.97) -> np.ndarray:
        """预加重处理"""
        return np.append(audio[0], audio[1:] - alpha * audio[:-1])
    
    def detect_voice_activity(self, audio: np.ndarray) -> List[Tuple[float, float]]:
        """语音活动检测"""
        # 转换为16位整数
        audio_int16 = (audio * 32767).astype(np.int16)
        
        voice_segments = []
        frame_count = len(audio_int16) // self.frame_size
        
        is_speech = False
        start_time = 0
        
        for i in range(frame_count):
            start_idx = i * self.frame_size
            end_idx = start_idx + self.frame_size
            frame = audio_int16[start_idx:end_idx]
            
            # VAD检测
            frame_is_speech = self.vad.is_speech(frame.tobytes(), self.sample_rate)
            
            if frame_is_speech and not is_speech:
                # 语音开始
                start_time = i * self.frame_duration / 1000.0
                is_speech = True
            elif not frame_is_speech and is_speech:
                # 语音结束
                end_time = i * self.frame_duration / 1000.0
                voice_segments.append((start_time, end_time))
                is_speech = False
        
        # 处理最后一个片段
        if is_speech:
            end_time = frame_count * self.frame_duration / 1000.0
            voice_segments.append((start_time, end_time))
        
        return voice_segments
    
    def extract_mfcc_features(self, audio: np.ndarray, n_mfcc: int = 13) -> np.ndarray:
        """提取MFCC特征"""
        # 动态调整FFT参数
        n_fft = min(2048, len(audio))
        if n_fft < 512:
            n_fft = 512
        hop_length = min(512, n_fft // 4)

        # 根据n_fft调整n_mels，避免空滤波器
        n_mels = min(128, n_fft // 4)  # 梅尔滤波器数量
        if n_mels < 40:  # MFCC需要足够的梅尔滤波器
            n_mels = 40

        # 确保n_mfcc不超过n_mels
        actual_n_mfcc = min(n_mfcc, n_mels // 2)
        if actual_n_mfcc < 13:
            actual_n_mfcc = min(13, n_mels // 3)

        # 确保音频长度足够
        if len(audio) < n_fft:
            audio = np.pad(audio, (0, n_fft - len(audio)), 'constant')

        mfcc = librosa.feature.mfcc(
            y=audio,
            sr=self.sample_rate,
            n_mfcc=actual_n_mfcc,
            n_mels=n_mels,
            n_fft=n_fft,
            hop_length=hop_length,
            fmax=self.sample_rate // 2
        )
        return mfcc
    
    def extract_mel_spectrogram(self, audio: np.ndarray, n_mels: int = 80) -> np.ndarray:
        """提取梅尔频谱图"""
        # 动态调整FFT参数
        n_fft = min(2048, len(audio))
        if n_fft < 512:
            n_fft = 512
        hop_length = min(512, n_fft // 4)

        # 根据n_fft调整n_mels，避免空滤波器
        # 经验规则：n_mels应该小于n_fft/2
        max_mels = min(n_mels, n_fft // 4)  # 保守一些
        if max_mels < 13:  # 至少保证13个梅尔滤波器
            max_mels = 13

        # 确保音频长度足够
        if len(audio) < n_fft:
            audio = np.pad(audio, (0, n_fft - len(audio)), 'constant')

        mel_spec = librosa.feature.melspectrogram(
            y=audio,
            sr=self.sample_rate,
            n_mels=max_mels,
            n_fft=n_fft,
            hop_length=hop_length,
            fmax=self.sample_rate // 2  # 明确设置最大频率
        )
        return librosa.power_to_db(mel_spec, ref=np.max)
    
    def extract_pitch(self, audio: np.ndarray) -> np.ndarray:
        """提取基频(F0)"""
        # 自适应调整基频提取参数
        frame_length, hop_length, fmin = self._get_adaptive_pitch_params(len(audio))

        # 确保音频长度足够
        if len(audio) < frame_length:
            audio = np.pad(audio, (0, frame_length - len(audio)), 'constant')

        f0, voiced_flag, voiced_probs = librosa.pyin(
            audio,
            fmin=fmin,
            fmax=500,  # 普通话基频范围
            sr=self.sample_rate,
            frame_length=frame_length,
            hop_length=hop_length
        )
        return f0

    def _get_adaptive_pitch_params(self, audio_length: int) -> Tuple[int, int, float]:
        """根据音频长度自适应调整基频提取参数"""
        if audio_length < 400:
            frame_length = max(256, audio_length)
            fmin = max(100, self.sample_rate * 2 / frame_length)
            hop_length = frame_length // 4
        elif audio_length < 640:
            frame_length = 400
            fmin = 80
            hop_length = 100
        elif audio_length < 1280:
            frame_length = 640
            fmin = 50
            hop_length = 160
        else:
            frame_length = 1024
            fmin = 50
            hop_length = 256

        return frame_length, hop_length, fmin
    
    def extract_formants(self, audio: np.ndarray) -> List[np.ndarray]:
        """提取共振峰"""
        # 使用LPC分析提取共振峰
        # 这里简化实现，实际应用中可能需要更复杂的算法
        
        # 计算LPC系数
        lpc_order = int(self.sample_rate / 1000) + 2
        frame_length = int(0.025 * self.sample_rate)  # 25ms窗口
        hop_length = int(0.01 * self.sample_rate)     # 10ms步长
        
        formants = []
        
        for i in range(0, len(audio) - frame_length, hop_length):
            frame = audio[i:i + frame_length]
            
            # 应用汉明窗
            windowed_frame = frame * np.hamming(len(frame))
            
            # 计算LPC系数
            lpc_coeffs = self._lpc_analysis(windowed_frame, lpc_order)
            
            # 从LPC系数计算共振峰
            frame_formants = self._lpc_to_formants(lpc_coeffs)
            formants.append(frame_formants)
        
        return formants
    
    def _lpc_analysis(self, signal: np.ndarray, order: int) -> np.ndarray:
        """LPC分析"""
        # 简化的LPC实现
        # 实际应用中建议使用更成熟的库如scipy.signal.lfilter
        autocorr = np.correlate(signal, signal, mode='full')
        autocorr = autocorr[len(autocorr)//2:]
        
        # Levinson-Durbin算法
        lpc_coeffs = np.zeros(order + 1)
        lpc_coeffs[0] = 1.0
        
        return lpc_coeffs
    
    def _lpc_to_formants(self, lpc_coeffs: np.ndarray) -> List[float]:
        """从LPC系数计算共振峰频率"""
        # 计算LPC多项式的根
        roots = np.roots(lpc_coeffs)
        
        # 选择复数根
        complex_roots = roots[np.imag(roots) > 0]
        
        # 转换为频率
        formant_freqs = []
        for root in complex_roots:
            freq = np.angle(root) * self.sample_rate / (2 * np.pi)
            if 200 < freq < 4000:  # 只保留合理的共振峰频率范围
                formant_freqs.append(freq)
        
        # 排序并返回前3个共振峰
        formant_freqs.sort()
        return formant_freqs[:3]
    
    def segment_audio(self, audio: np.ndarray, segments: List[Tuple[float, float]]) -> List[np.ndarray]:
        """根据时间段分割音频"""
        segmented_audio = []
        
        for start_time, end_time in segments:
            start_sample = int(start_time * self.sample_rate)
            end_sample = int(end_time * self.sample_rate)
            
            segment = audio[start_sample:end_sample]
            segmented_audio.append(segment)
        
        return segmented_audio
    
    def calculate_energy(self, audio: np.ndarray) -> float:
        """计算音频能量"""
        return np.sum(audio ** 2) / len(audio)
    
    def calculate_zero_crossing_rate(self, audio: np.ndarray) -> float:
        """计算过零率"""
        return np.sum(np.abs(np.diff(np.sign(audio)))) / (2 * len(audio))


class PitchExtractor:
    """基频提取器"""
    
    def __init__(self, sample_rate: int = 16000):
        self.sample_rate = sample_rate
    
    def extract_pitch(self, audio: np.ndarray) -> np.ndarray:
        """提取基频轮廓"""
        # 自适应调整基频提取参数
        frame_length, hop_length, fmin = self._get_adaptive_pitch_params(len(audio))

        # 确保音频长度足够
        if len(audio) < frame_length:
            audio = np.pad(audio, (0, frame_length - len(audio)), 'constant')

        f0, voiced_flag, voiced_probs = librosa.pyin(
            audio,
            fmin=fmin,
            fmax=500,   # 最高频率
            sr=self.sample_rate,
            frame_length=frame_length,
            hop_length=hop_length
        )

        # 平滑处理
        f0_smooth = self._smooth_pitch(f0, voiced_flag)

        return f0_smooth

    def _get_adaptive_pitch_params(self, audio_length: int) -> Tuple[int, int, float]:
        """根据音频长度自适应调整基频提取参数"""
        if audio_length < 400:
            frame_length = max(256, audio_length)
            fmin = max(100, self.sample_rate * 2 / frame_length)
            hop_length = frame_length // 4
        elif audio_length < 640:
            frame_length = 400
            fmin = 80
            hop_length = 100
        elif audio_length < 1280:
            frame_length = 640
            fmin = 50
            hop_length = 160
        else:
            frame_length = 1024
            fmin = 50
            hop_length = 256

        return frame_length, hop_length, fmin
    
    def _smooth_pitch(self, f0: np.ndarray, voiced_flag: np.ndarray) -> np.ndarray:
        """基频平滑处理"""
        # 对有声段进行中值滤波
        f0_smooth = f0.copy()
        
        # 中值滤波
        from scipy.ndimage import median_filter
        f0_smooth[voiced_flag] = median_filter(f0[voiced_flag], size=3)
        
        return f0_smooth

    def _extract_formants_simple(self, audio: np.ndarray) -> List[float]:
        """简单的共振峰提取"""
        try:
            if len(audio) < 100:
                return [0, 0, 0]

            # 使用LPC分析提取共振峰
            from scipy.signal import lfilter

            # 预加重
            pre_emphasis = 0.97
            emphasized_audio = np.append(audio[0], audio[1:] - pre_emphasis * audio[:-1])

            # 窗函数
            windowed = emphasized_audio * np.hamming(len(emphasized_audio))

            # 自相关
            autocorr = np.correlate(windowed, windowed, mode='full')
            autocorr = autocorr[len(autocorr)//2:]

            # 简化的共振峰估计
            formants = []
            if len(autocorr) > 10:
                # 找峰值作为共振峰估计
                peaks = []
                for i in range(1, min(len(autocorr)-1, 100)):
                    if autocorr[i] > autocorr[i-1] and autocorr[i] > autocorr[i+1]:
                        freq = i * self.sample_rate / len(autocorr)
                        if 200 < freq < 4000:  # 合理的共振峰范围
                            peaks.append(freq)

                formants = sorted(peaks)[:3]  # 取前3个共振峰

            # 填充到3个共振峰
            while len(formants) < 3:
                formants.append(0)

            return formants[:3]

        except Exception as e:
            logger.error(f"共振峰提取失败: {e}")
            return [0, 0, 0]


class FormantAnalyzer:
    """共振峰分析器"""
    
    def __init__(self, sample_rate: int = 16000):
        self.sample_rate = sample_rate
    
    def analyze(self, audio: np.ndarray) -> List[float]:
        """分析共振峰"""
        # 使用更精确的共振峰提取算法
        # 这里可以集成Praat的算法或其他专业工具
        
        processor = AudioProcessor(self.sample_rate)
        formants = processor.extract_formants(audio)
        
        if formants:
            # 返回平均共振峰值
            avg_formants = np.mean(formants, axis=0)
            return avg_formants.tolist()
        
        return [0, 0, 0]  # 默认返回三个共振峰
